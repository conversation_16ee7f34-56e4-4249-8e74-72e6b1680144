{"configurations": [{"name": "LoggerLH_Dev", "includePath": ["d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Device\\ST\\STM32L4xx\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Nucleo_144", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\embedded-libs", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\RTT", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Hermes_LH", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\MDK-ARM", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src\\option", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src"], "defines": ["STM32L4R5xx", "USE_HAL_DRIVER", "USE_STM32L4XX_NUCLEO_144", "HSE_VALUE=16000000", "SEGGER_RTT_SECTION", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "LoggerLH_PRD", "includePath": ["d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Device\\ST\\STM32L4xx\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Nucleo_144", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\embedded-libs", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\RTT", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Hermes_LH", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\MDK-ARM", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src\\option", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src"], "defines": ["STM32L4R5xx", "USE_HAL_DRIVER", "USE_STM32L4XX_NUCLEO_144", "HSE_VALUE=16000000", "PRODUCT_BUILD", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "Logger_PRD", "includePath": ["d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Device\\ST\\STM32L4xx\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Nucleo_144", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\embedded-libs", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\RTT", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Hermes_LH", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\MDK-ARM", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src\\option", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src"], "defines": ["STM32L4R5xx", "USE_HAL_DRIVER", "USE_STM32L4XX_NUCLEO_144", "HSE_VALUE=16000000", "PRODUCT_BUILD", "__LIGHTHOUSE_DISABLE__", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "<PERSON><PERSON>_<PERSON>", "includePath": ["d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\CMSIS\\Device\\ST\\STM32L4xx\\Include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Nucleo_144", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\include", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\embedded-libs", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Inc", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\RTT", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\BSP\\STM32L4xx_Hermes_LH", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\MDK-ARM", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Drivers\\STM32L4xx_HAL_Driver\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Projects\\NUCLEO-L4R5ZI\\Applications\\FreeRTOS\\FreeRTOS_InstanLogger_Lighthouse", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\Third_Party\\FatFs\\src\\option", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src", "d:\\Keil MDK\\Keil Project\\hermes_firmware\\STM32Cube_FW_L4_V1.14.0\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src"], "defines": ["STM32L4R5xx", "USE_HAL_DRIVER", "USE_STM32L4XX_NUCLEO_144", "HSE_VALUE=16000000", "SEGGER_RTT_SECTION", "__LIGHTHOUSE_DISABLE__", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}